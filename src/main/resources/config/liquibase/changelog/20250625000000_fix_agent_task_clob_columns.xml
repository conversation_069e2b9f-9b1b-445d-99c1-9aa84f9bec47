<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <!--
        修复 AgentTask 表的 CLOB 字段长度限制问题
        将 TEXT 类型改为 LONGTEXT 以支持更大的数据存储
    -->
    <changeSet id="20250625000000-1" author="jhipster">
        <comment>修复 AgentTask 表的 CLOB 字段，将 TEXT 改为 LONGTEXT</comment>
        
        <!-- 修改 request_data 字段为 LONGTEXT -->
        <modifyDataType tableName="agent_task" columnName="request_data" newDataType="LONGTEXT"/>
        
        <!-- 修改 response_data 字段为 LONGTEXT -->
        <modifyDataType tableName="agent_task" columnName="response_data" newDataType="LONGTEXT"/>
        
        <!-- 修改 metadata 字段为 LONGTEXT -->
        <modifyDataType tableName="agent_task" columnName="metadata" newDataType="LONGTEXT"/>
    </changeSet>

    <!--
        同时修复 TaskStep 表的 CLOB 字段
    -->
    <changeSet id="20250625000000-2" author="jhipster">
        <comment>修复 TaskStep 表的 CLOB 字段，将 TEXT 改为 LONGTEXT</comment>
        
        <!-- 修改 input_data 字段为 LONGTEXT -->
        <modifyDataType tableName="task_step" columnName="input_data" newDataType="LONGTEXT"/>
        
        <!-- 修改 output_data 字段为 LONGTEXT -->
        <modifyDataType tableName="task_step" columnName="output_data" newDataType="LONGTEXT"/>
        
        <!-- 修改 metadata 字段为 LONGTEXT -->
        <modifyDataType tableName="task_step" columnName="metadata" newDataType="LONGTEXT"/>
    </changeSet>

</databaseChangeLog>
