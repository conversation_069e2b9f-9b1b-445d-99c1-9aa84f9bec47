/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：ComplianceAgentController.java
 * 包    名：com.whiskerguard.ai.web.rest
 * 描    述：合规智能体REST控制器
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.web.rest;

import com.whiskerguard.ai.service.agent.ComplianceAgentService;
import com.whiskerguard.ai.service.agent.dto.AgentTaskRequestDTO;
import com.whiskerguard.ai.service.agent.dto.AgentTaskResponseDTO;
import com.whiskerguard.ai.service.agent.dto.AgentTaskStatusDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

/**
 * 合规智能体管理
 *
 * <p>
 * 提供合规智能体的统一API接口，支持三大核心功能：
 * 1. 外规内化（将国家法规转化为企业内部制度）
 * 2. 制度审查（审查企业内部制度的合规性）
 * 3. 合同审查（审查合同的合规性和风险）
 *
 * 该控制器作为Agent功能的统一入口，与现有控制器协同工作。
 *
 * <AUTHOR>
 * @since 1.0
 */
@RestController
@RequestMapping("/api/compliance-agent")
@Tag(name = "合规智能体", description = "合规智能体API接口")
public class ComplianceAgentController {

    private static final Logger log = LoggerFactory.getLogger(ComplianceAgentController.class);

    private final ComplianceAgentService complianceAgentService;

    /**
     * 构造函数注入依赖
     */
    @Autowired
    public ComplianceAgentController(ComplianceAgentService complianceAgentService) {
        this.complianceAgentService = complianceAgentService;
    }

    /**
     * 创建Agent任务
     *
     * @param request 任务请求
     * @param bindingResult 验证结果
     * @return 任务响应
     */
    @PostMapping("/tasks")
    @Operation(summary = "创建Agent任务", description = "创建一个新的合规智能体任务")
    @ApiResponses(
        value = {
            @ApiResponse(
                responseCode = "201",
                description = "任务创建成功",
                content = @Content(schema = @Schema(implementation = AgentTaskResponseDTO.class))
            ),
            @ApiResponse(responseCode = "400", description = "请求参数无效"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误"),
        }
    )
    public ResponseEntity<?> createTask(@Valid @RequestBody AgentTaskRequestDTO request, BindingResult bindingResult) {
        log.info("收到创建Agent任务请求，类型: {}, 租户: {}", request.getTaskType(), request.getTenantId());

        try {
            // 参数验证
            if (bindingResult.hasErrors()) {
                Map<String, String> errors = new HashMap<>();
                bindingResult.getFieldErrors().forEach(error -> errors.put(error.getField(), error.getDefaultMessage()));
                return ResponseEntity.badRequest().body(errors);
            }

            // 创建任务
            AgentTaskResponseDTO response = complianceAgentService.createTask(request);

            log.info("Agent任务创建成功，任务ID: {}", response.getTaskId());
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } catch (Exception e) {
            log.error("创建Agent任务失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("error", "创建任务失败: " + e.getMessage()));
        }
    }

    /**
     * 获取任务状态
     *
     * @param taskId 任务ID
     * @return 任务状态
     */
    @GetMapping("/tasks/{taskId}/status")
    @Operation(summary = "获取任务状态", description = "获取指定任务的当前状态")
    @ApiResponses(
        value = {
            @ApiResponse(
                responseCode = "200",
                description = "获取状态成功",
                content = @Content(schema = @Schema(implementation = AgentTaskStatusDTO.class))
            ),
            @ApiResponse(responseCode = "404", description = "任务不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误"),
        }
    )
    public ResponseEntity<?> getTaskStatus(@Parameter(description = "任务ID", required = true) @PathVariable Long taskId) {
        log.debug("获取任务状态，任务ID: {}", taskId);

        try {
            AgentTaskResponseDTO status = complianceAgentService.getTaskStatus(taskId);
            return ResponseEntity.ok(status);
        } catch (RuntimeException e) {
            if (e.getMessage().contains("任务不存在")) {
                return ResponseEntity.notFound().build();
            }
            log.error("获取任务状态失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("error", "获取任务状态失败: " + e.getMessage()));
        }
    }

    /**
     * 获取任务结果
     *
     * @param taskId 任务ID
     * @return 任务结果
     */
    @GetMapping("/tasks/{taskId}/result")
    @Operation(summary = "获取任务结果", description = "获取指定任务的执行结果")
    @ApiResponses(
        value = {
            @ApiResponse(
                responseCode = "200",
                description = "获取结果成功",
                content = @Content(schema = @Schema(implementation = AgentTaskResponseDTO.class))
            ),
            @ApiResponse(responseCode = "404", description = "任务不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误"),
        }
    )
    public ResponseEntity<?> getTaskResult(@Parameter(description = "任务ID", required = true) @PathVariable Long taskId) {
        log.debug("获取任务结果，任务ID: {}", taskId);

        try {
            AgentTaskResponseDTO result = complianceAgentService.getTaskResult(taskId);
            return ResponseEntity.ok(result);
        } catch (RuntimeException e) {
            if (e.getMessage().contains("任务不存在")) {
                return ResponseEntity.notFound().build();
            }
            log.error("获取任务结果失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("error", "获取任务结果失败: " + e.getMessage()));
        }
    }

    /**
     * 取消任务
     *
     * @param taskId 任务ID
     * @return 操作结果
     */
    @PostMapping("/tasks/{taskId}/cancel")
    @Operation(summary = "取消任务", description = "取消指定的任务执行")
    @ApiResponses(
        value = {
            @ApiResponse(responseCode = "200", description = "任务取消成功"),
            @ApiResponse(responseCode = "404", description = "任务不存在"),
            @ApiResponse(responseCode = "500", description = "服务器内部错误"),
        }
    )
    public ResponseEntity<?> cancelTask(@Parameter(description = "任务ID", required = true) @PathVariable Long taskId) {
        log.info("取消任务，任务ID: {}", taskId);

        try {
            complianceAgentService.cancelTask(taskId);
            return ResponseEntity.ok(Map.of("message", "任务取消成功"));
        } catch (RuntimeException e) {
            if (e.getMessage().contains("任务不存在")) {
                return ResponseEntity.notFound().build();
            }
            log.error("取消任务失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("error", "取消任务失败: " + e.getMessage()));
        }
    }

    /**
     * 健康检查
     *
     * @return 健康状态
     */
    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "检查合规智能体服务的健康状态")
    @ApiResponse(responseCode = "200", description = "服务正常")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("service", "compliance-agent");
        health.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.ok(health);
    }
}
