package com.whiskerguard.ai.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.time.Instant;

@Schema(description = "外规内化响应DTO")
public class RegulationInternalizationResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "法规ID")
    private String regulationId;

    @Schema(description = "公司ID")
    private Long companyId;

    @Schema(description = "内化结果")
    private String internalizationResult;

    @Schema(description = "内部制度内容")
    private String internalPolicyContent;

    @Schema(description = "合规要点")
    private String compliancePoints;

    @Schema(description = "实施建议")
    private String implementationSuggestions;

    @Schema(description = "风险提示")
    private String riskWarnings;

    @Schema(description = "合规评分")
    private Double complianceScore;

    @Schema(description = "处理状态")
    private String status;

    @Schema(description = "处理时间")
    private Instant processedAt;

    public RegulationInternalizationResponseDTO() {}

    // Getter和Setter方法
    public String getRegulationId() {
        return regulationId;
    }

    public void setRegulationId(String regulationId) {
        this.regulationId = regulationId;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getInternalizationResult() {
        return internalizationResult;
    }

    public void setInternalizationResult(String internalizationResult) {
        this.internalizationResult = internalizationResult;
    }

    public String getInternalPolicyContent() {
        return internalPolicyContent;
    }

    public void setInternalPolicyContent(String internalPolicyContent) {
        this.internalPolicyContent = internalPolicyContent;
    }

    public String getCompliancePoints() {
        return compliancePoints;
    }

    public void setCompliancePoints(String compliancePoints) {
        this.compliancePoints = compliancePoints;
    }

    public String getImplementationSuggestions() {
        return implementationSuggestions;
    }

    public void setImplementationSuggestions(String implementationSuggestions) {
        this.implementationSuggestions = implementationSuggestions;
    }

    public String getRiskWarnings() {
        return riskWarnings;
    }

    public void setRiskWarnings(String riskWarnings) {
        this.riskWarnings = riskWarnings;
    }

    public Double getComplianceScore() {
        return complianceScore;
    }

    public void setComplianceScore(Double complianceScore) {
        this.complianceScore = complianceScore;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Instant getProcessedAt() {
        return processedAt;
    }

    public void setProcessedAt(Instant processedAt) {
        this.processedAt = processedAt;
    }

    // Builder模式
    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {

        private String regulationId;
        private Long companyId;
        private String internalizationResult;
        private String internalPolicyContent;
        private String compliancePoints;
        private String implementationSuggestions;
        private String riskWarnings;
        private Double complianceScore;
        private String status;
        private Instant processedAt;

        public Builder regulationId(String regulationId) {
            this.regulationId = regulationId;
            return this;
        }

        public Builder companyId(Long companyId) {
            this.companyId = companyId;
            return this;
        }

        public Builder internalizationResult(String internalizationResult) {
            this.internalizationResult = internalizationResult;
            return this;
        }

        public Builder internalPolicyContent(String internalPolicyContent) {
            this.internalPolicyContent = internalPolicyContent;
            return this;
        }

        public Builder compliancePoints(String compliancePoints) {
            this.compliancePoints = compliancePoints;
            return this;
        }

        public Builder implementationSuggestions(String implementationSuggestions) {
            this.implementationSuggestions = implementationSuggestions;
            return this;
        }

        public Builder riskWarnings(String riskWarnings) {
            this.riskWarnings = riskWarnings;
            return this;
        }

        public Builder complianceScore(double complianceScore) {
            this.complianceScore = complianceScore;
            return this;
        }

        public Builder status(String status) {
            this.status = status;
            return this;
        }

        public Builder processedAt(Instant processedAt) {
            this.processedAt = processedAt;
            return this;
        }

        public RegulationInternalizationResponseDTO build() {
            RegulationInternalizationResponseDTO dto = new RegulationInternalizationResponseDTO();
            dto.setRegulationId(regulationId);
            dto.setCompanyId(companyId);
            dto.setInternalizationResult(internalizationResult);
            dto.setInternalPolicyContent(internalPolicyContent);
            dto.setCompliancePoints(compliancePoints);
            dto.setImplementationSuggestions(implementationSuggestions);
            dto.setRiskWarnings(riskWarnings);
            dto.setComplianceScore(complianceScore);
            dto.setStatus(status);
            dto.setProcessedAt(processedAt);
            return dto;
        }
    }
}
