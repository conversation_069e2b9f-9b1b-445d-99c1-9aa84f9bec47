/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：RegulationInternalizationAgentService.java
 * 包    名：com.whiskerguard.ai.service.agent.business
 * 描    述：外规内化智能体服务
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.agent.business;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.client.RetrievalServiceClient;
import com.whiskerguard.ai.client.dto.CompanyInfoDTO;
import com.whiskerguard.ai.service.agent.core.KnowledgeRetrievalService;
import com.whiskerguard.ai.service.agent.core.LlmOrchestrationService;
import com.whiskerguard.ai.service.agent.core.LlmOrchestrationService.ChainLlmRequest;
import com.whiskerguard.ai.service.agent.core.LlmOrchestrationService.ChainStrategy;
import com.whiskerguard.ai.service.compliance.ComplianceVerificationService;
import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import com.whiskerguard.ai.service.dto.RegulationInternalizationRequestDTO;
import com.whiskerguard.ai.service.dto.RegulationInternalizationResponseDTO;
import com.whiskerguard.ai.service.invocation.AiInvocationService;
import com.whiskerguard.ai.service.invocation.ModelEnsembleService;
import com.whiskerguard.ai.service.invocation.RagHelper;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * 外规内化智能体服务
 * <p>
 * 负责将国家法律法规转化为企业内部管理制度。
 * 通过AI分析法规内容，结合企业特点生成内部制度。
 *
 * 主要功能：
 * 1. 法规内容解析
 * 2. 行业特点分析
 * 3. 企业制度生成
 * 4. 合规性验证
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class RegulationInternalizationAgentService {

    private static final Logger log = LoggerFactory.getLogger(RegulationInternalizationAgentService.class);

    private final KnowledgeRetrievalService knowledgeRetrievalService;
    private final LlmOrchestrationService llmOrchestrationService;
    private final AiInvocationService aiInvocationService;
    private final RetrievalServiceClient retrievalServiceClient;
    private final RagHelper ragHelper;
    private final ModelEnsembleService modelEnsembleService;
    private final ComplianceVerificationService complianceVerificationService;
    private final ObjectMapper objectMapper;

    /**
     * 默认使用的工具Key
     */
    private static final String DEFAULT_TOOL_KEY = "kimi";

    /**
     * 构造函数注入依赖
     */
    @Autowired
    public RegulationInternalizationAgentService(
        KnowledgeRetrievalService knowledgeRetrievalService,
        LlmOrchestrationService llmOrchestrationService,
        AiInvocationService aiInvocationService,
        RetrievalServiceClient retrievalServiceClient,
        RagHelper ragHelper,
        ModelEnsembleService modelEnsembleService,
        ComplianceVerificationService complianceVerificationService,
        ObjectMapper objectMapper
    ) {
        this.knowledgeRetrievalService = knowledgeRetrievalService;
        this.llmOrchestrationService = llmOrchestrationService;
        this.aiInvocationService = aiInvocationService;
        this.retrievalServiceClient = retrievalServiceClient;
        this.ragHelper = ragHelper;
        this.modelEnsembleService = modelEnsembleService;
        this.complianceVerificationService = complianceVerificationService;
        this.objectMapper = objectMapper;
    }

    /**
     * 处理外规内化请求
     *
     * @param request 外规内化请求
     * @return 外规内化响应
     */
    public RegulationInternalizationResponseDTO processInternalization(RegulationInternalizationRequestDTO request) {
        log.info("开始处理外规内化请求，法规ID: {}, 企业行业: {}", request.getRegulationId(), request.getIndustryType());

        try {
            // 1. 检索相关法规内容
            String regulationContent = retrieveRegulationContent(request);
            if (regulationContent == null || regulationContent.isBlank()) {
                //throw new IllegalArgumentException("无法获取法规内容，请确认法规ID是否正确");
            }

            // 2. 检索行业最佳实践
            String industryPractices = retrieveIndustryPractices(request);

            // 3. 检索企业现有制度
            String existingPolicies = retrieveExistingPolicies(request);

            // 4. 创建企业信息DTO
            CompanyInfoDTO companyInfo = createCompanyInfoDTO(request);

            // 5. 构建增强提示词（复用RegulatoryPolicyController的RAG增强逻辑）
            String tenantIdStr = request.getTenantId() != null ? request.getTenantId().toString() : null;
            // 类型转换，将 client.dto.CompanyInfoDTO 转换为 service.compliance.CompanyInfoDTO
            com.whiskerguard.ai.service.compliance.CompanyInfoDTO serviceCompanyInfo = convertToServiceCompanyInfoDTO(companyInfo);
            String enhancedPrompt = ragHelper.enhancePromptForRegulatoryPolicy(regulationContent, serviceCompanyInfo, tenantIdStr);

            // 6. 生成内部制度（复用多模型整合策略）
            String internalPolicy = generateInternalPolicy(request, enhancedPrompt);

            // 7. 验证合规性
            Map<String, Object> complianceCheckResult = validateCompliance(internalPolicy, regulationContent, companyInfo);

            // 8. 构建响应
            return buildResponse(request, internalPolicy, complianceCheckResult);
        } catch (Exception e) {
            log.error("处理外规内化请求失败", e);
            throw new RuntimeException("外规内化处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检索法规内容
     * 利用优化后的KnowledgeRetrievalService实现高效检索和缓存
     */
    private String retrieveRegulationContent(RegulationInternalizationRequestDTO request) {
        log.debug("检索法规内容，法规ID: {}", request.getRegulationId());

        try {
            // 构建查询参数
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("regulationId", request.getRegulationId());

            // 如果提供了法规名称，添加到查询参数
            if (request.getRegulationName() != null && !request.getRegulationName().isBlank()) {
                queryParams.put("regulationName", request.getRegulationName());
            }

            // 构建唯一的缓存键
            String cacheKey = "regulation_" + request.getRegulationId();

            // 利用KnowledgeRetrievalService进行检索，自动处理缓存
            return knowledgeRetrievalService.retrieveKnowledge(request.getTenantId(), "REGULATION", cacheKey, queryParams);
        } catch (Exception e) {
            log.error("检索法规内容失败，法规ID: {}, 错误信息: {}", request.getRegulationId(), e.getMessage(), e);
            return ""; // 返回空字符串，避免空指针异常
        }
    }

    /**
     * 检索行业最佳实践
     * 利用优化后的KnowledgeRetrievalService实现高效检索和缓存
     */
    private String retrieveIndustryPractices(RegulationInternalizationRequestDTO request) {
        log.debug("检索行业最佳实践，行业类型: {}", request.getIndustryType());

        try {
            // 构建查询参数
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("industryType", request.getIndustryType());

            // 如果提供了行业分类代码，添加到查询参数
            if (request.getIndustryCode() != null && !request.getIndustryCode().isBlank()) {
                queryParams.put("industryCode", request.getIndustryCode());
            }

            // 如果提供了法规ID，添加到查询参数以检索相关实践
            if (request.getRegulationId() != null && !request.getRegulationId().isBlank()) {
                queryParams.put("relatedRegulation", request.getRegulationId());
            }

            // 构建唯一的缓存键
            String cacheKey = "industry_practice_" + request.getIndustryType();

            // 利用KnowledgeRetrievalService进行检索，自动处理缓存
            return knowledgeRetrievalService.retrieveKnowledge(request.getTenantId(), "INDUSTRY_PRACTICE", cacheKey, queryParams);
        } catch (Exception e) {
            log.error("检索行业最佳实践失败，行业类型: {}, 错误信息: {}", request.getIndustryType(), e.getMessage(), e);
            return ""; // 返回空字符串，避免空指针异常
        }
    }

    /**
     * 检索企业现有制度
     * 利用优化后的KnowledgeRetrievalService实现高效检索和缓存
     */
    private String retrieveExistingPolicies(RegulationInternalizationRequestDTO request) {
        log.debug("检索企业现有制度，企业ID: {}", request.getCompanyId());

        try {
            // 构建查询参数
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("companyId", request.getCompanyId());

            // 如果提供了制度类型，添加到查询参数
            if (request.getPolicyType() != null && !request.getPolicyType().isBlank()) {
                queryParams.put("policyType", request.getPolicyType());
            }

            // 如果提供了法规ID，添加到查询参数以检索相关制度
            if (request.getRegulationId() != null && !request.getRegulationId().isBlank()) {
                queryParams.put("relatedRegulation", request.getRegulationId());
            }

            // 构建唯一的缓存键
            String cacheKey = "company_policy_" + request.getCompanyId();

            // 利用KnowledgeRetrievalService进行检索，自动处理缓存
            return knowledgeRetrievalService.retrieveKnowledge(request.getTenantId(), "COMPANY_POLICY", cacheKey, queryParams);
        } catch (Exception e) {
            log.error("检索企业现有制度失败，企业ID: {}, 错误信息: {}", request.getCompanyId(), e.getMessage(), e);
            return ""; // 返回空字符串，避免空指针异常
        }
    }

    /**
     * 创建企业信息DTO
     */
    private CompanyInfoDTO createCompanyInfoDTO(RegulationInternalizationRequestDTO request) {
        try {
            CompanyInfoDTO companyInfo = new CompanyInfoDTO();
            companyInfo.setId(request.getCompanyId());
            companyInfo.setName(request.getCompanyName());

            Map<String, Object> additionalInfo = new HashMap<>();
            additionalInfo.put("industry", request.getIndustryType());
            additionalInfo.put("scale", request.getCompanyScale());

            if (request.getCompanyAttributes() != null && !request.getCompanyAttributes().isEmpty()) {
                additionalInfo.putAll(request.getCompanyAttributes());
            }

            companyInfo.setAdditionalInfo(additionalInfo);

            return companyInfo;
        } catch (Exception e) {
            log.error("创建企业信息DTO失败，企业ID: {}, 错误信息: {}", request.getCompanyId(), e.getMessage(), e);
            // 创建一个基础的DTO，确保后续流程可以继续
            CompanyInfoDTO fallbackInfo = new CompanyInfoDTO();
            fallbackInfo.setId(request.getCompanyId());
            if (request.getCompanyName() != null) {
                fallbackInfo.setName(request.getCompanyName());
            } else {
                fallbackInfo.setName("未命名企业");
            }
            fallbackInfo.setAdditionalInfo(new HashMap<>());
            return fallbackInfo;
        }
    }

    /**
     * 生成内部制度
     * 利用优化后的LlmOrchestrationService实现高效的多模型调用和结果整合
     */
    private String generateInternalPolicy(RegulationInternalizationRequestDTO request, String enhancedPrompt) {
        log.debug("生成内部制度");

        try {
            // 创建AI调用请求
            AiInvocationRequestDTO aiRequest = new AiInvocationRequestDTO(
                DEFAULT_TOOL_KEY, // 工具Key
                enhancedPrompt, // 增强提示词
                null, // 元数据，当前不需要
                request.getTenantId(),
                request.getEmployeeId()
            );

            // 确定要使用的模型列表
            List<String> modelsList;
            if (request.getModelNames() != null && request.getModelNames().length > 0) {
                modelsList = Arrays.asList(request.getModelNames());
            } else {
                // 默认使用三种模型
                modelsList = Arrays.asList("doubao", "kimi");
            }

            // 使用ModelEnsembleService生成多模型整合的响应
            String integratedResponse = modelEnsembleService.generateEnsembleResponse(aiRequest, modelsList);
            log.debug("多模型整合响应生成完成，长度: {}", integratedResponse.length());

            return integratedResponse;
        } catch (Exception e) {
            log.error("生成内部制度失败，错误信息: {}", e.getMessage(), e);
            // 返回一个基本的模板，以确保流程可以继续
            return "内部制度模板\n\n第一章 总则\n本制度根据相关法律法规制定，适用于公司全体员工。\n\n第二章 基本要求\n公司应当遵守相关法律法规，建立健全管理制度。\n\n第三章 实施与监督\n公司应指定专人负责本制度的实施和监督。\n\n（注：AI生成遇到技术问题，请人工审核后再完善本制度）";
        }
    }

    /**
     * 验证合规性
     * 利用ComplianceVerificationService验证生成制度的合规性
     */
    private Map<String, Object> validateCompliance(String internalPolicy, String regulationContent, CompanyInfoDTO clientCompanyInfo) {
        log.debug("验证合规性");

        try {
            // 将client.dto.CompanyInfoDTO转换为service.compliance.CompanyInfoDTO
            com.whiskerguard.ai.service.compliance.CompanyInfoDTO complianceCompanyInfo = convertToComplianceDTO(clientCompanyInfo);

            // 使用ComplianceVerificationService验证合规性
            ComplianceVerificationService.VerificationResult result = complianceVerificationService.verify(
                internalPolicy,
                regulationContent,
                complianceCompanyInfo
            );

            log.debug("内容验证完成，合规度评分: {}", result.getComplianceScore());

            // 将结果转换为Map返回
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("verifiedContent", result.getVerifiedContent());
            resultMap.put("annotations", result.getAnnotations());
            resultMap.put("complianceScore", result.getComplianceScore());

            return resultMap;
        } catch (Exception e) {
            log.error("验证合规性失败，错误信息: {}", e.getMessage(), e);

            // 创建一个默认的结果，确保流程可以继续
            Map<String, Object> defaultResult = new HashMap<>();
            defaultResult.put("verifiedContent", internalPolicy); // 使用原始内容
            defaultResult.put("annotations", new ArrayList<String>());
            defaultResult.put("complianceScore", 70); // 默认给一个中等分数

            return defaultResult;
        }
    }

    /**
     * 将client.dto.CompanyInfoDTO转换为service.compliance.CompanyInfoDTO
     * 用于RagHelper调用
     */
    private com.whiskerguard.ai.service.compliance.CompanyInfoDTO convertToServiceCompanyInfoDTO(CompanyInfoDTO clientDTO) {
        return convertToComplianceDTO(clientDTO);
    }

    /**
     * 将client.dto.CompanyInfoDTO转换为service.compliance.CompanyInfoDTO
     */
    private com.whiskerguard.ai.service.compliance.CompanyInfoDTO convertToComplianceDTO(CompanyInfoDTO clientDTO) {
        com.whiskerguard.ai.service.compliance.CompanyInfoDTO complianceDTO = new com.whiskerguard.ai.service.compliance.CompanyInfoDTO();

        // 设置基本属性
        complianceDTO.setName(clientDTO.getName());

        // 从additionalInfo中提取更多信息
        Map<String, Object> additionalInfo = clientDTO.getAdditionalInfo();
        if (additionalInfo != null) {
            if (additionalInfo.containsKey("industry")) {
                complianceDTO.setIndustry((String) additionalInfo.get("industry"));
            }
            if (additionalInfo.containsKey("scale")) {
                complianceDTO.setScale((String) additionalInfo.get("scale"));
            }
            if (additionalInfo.containsKey("type")) {
                complianceDTO.setType((String) additionalInfo.get("type"));
            }
            // 可以根据需要添加更多属性映射
        }

        return complianceDTO;
    }

    /**
     * 构建响应
     */
    private RegulationInternalizationResponseDTO buildResponse(
        RegulationInternalizationRequestDTO request,
        String internalPolicy,
        Map<String, Object> complianceCheck
    ) {
        // 提取合规检查结果
        String verifiedContent = (String) complianceCheck.get("verifiedContent");
        Double complianceScore = (Double) complianceCheck.get("complianceScore");

        // 修复类型转换问题：annotations可能是List或HashMap
        List<String> annotations = new ArrayList<>();
        Object annotationsObj = complianceCheck.get("annotations");
        if (annotationsObj instanceof List) {
            annotations = (List<String>) annotationsObj;
        } else if (annotationsObj instanceof Map) {
            Map<?, ?> annotationsMap = (Map<?, ?>) annotationsObj;
            // 将Map中的值添加到List中
            List<String> finalAnnotations = annotations;
            annotationsMap.values().forEach(value -> {
                if (value != null) {
                    finalAnnotations.add(value.toString());
                }
            });
        } else if (annotationsObj != null) {
            // 如果是其他类型，转换为字符串并添加到列表
            annotations.add(annotationsObj.toString());
        }

        // 将合规点转换为字符串格式
        String compliancePoints = convertAnnotationsToString(annotations);

        // 根据合规性评分生成建议
        String implementationSuggestions = generateImplementationSuggestions(complianceScore, annotations);

        return RegulationInternalizationResponseDTO.builder()
            .regulationId(request.getRegulationId())
            .companyId(request.getCompanyId())
            .internalizationResult(verifiedContent)
            .compliancePoints(compliancePoints)
            .implementationSuggestions(implementationSuggestions)
            .complianceScore(complianceScore)
            .status("COMPLETED")
            .processedAt(Instant.now())
            .build();
    }

    /**
     * 将注释列表转换为字符串
     */
    private String convertAnnotationsToString(List<String> annotations) {
        if (annotations == null || annotations.isEmpty()) {
            return "无特别合规要点";
        }

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < annotations.size(); i++) {
            sb.append(i + 1).append(". ").append(annotations.get(i));
            if (i < annotations.size() - 1) {
                sb.append("\n");
            }
        }

        return sb.toString();
    }

    /**
     * 根据合规性评分生成实施建议
     */
    private String generateImplementationSuggestions(Double complianceScore, List<String> annotations) {
        StringBuilder suggestions = new StringBuilder();

        // 根据评分添加总体评价
        if (complianceScore >= 90) {
            suggestions.append("制度合规性评分优秀，可直接实施。\n\n");
        } else if (complianceScore >= 75) {
            suggestions.append("制度合规性评分良好，建议根据以下建议进行微调后实施。\n\n");
        } else if (complianceScore >= 60) {
            suggestions.append("制度合规性评分一般，需要根据以下建议进行修改后再实施。\n\n");
        } else {
            suggestions.append("制度合规性评分较低，建议根据以下建议进行重大修改后再考虑实施。\n\n");
        }

        // 添加具体建议
        suggestions.append("实施建议：\n");

        // 根据合规性检查结果生成建议
        if (annotations != null && !annotations.isEmpty()) {
            for (int i = 0; i < Math.min(5, annotations.size()); i++) {
                String annotation = annotations.get(i);
                // 从注释中提取建议
                if (annotation.contains("建议")) {
                    suggestions.append(i + 1).append(". ").append(annotation).append("\n");
                } else {
                    suggestions.append(i + 1).append(". 关于\"").append(annotation).append("\"，建议确保相关条款准确且可执行。\n");
                }
            }
        } else {
            suggestions.append("1. 建议在实施前进行内部法务审核\n");
            suggestions.append("2. 确保制度与公司现有管理架构相适应\n");
            suggestions.append("3. 对相关人员进行培训，确保理解新制度\n");
        }

        // 添加通用建议
        suggestions.append("\n后续步骤：\n");
        suggestions.append("1. 组织相关部门进行制度评审\n");
        suggestions.append("2. 提交管理层审批\n");
        suggestions.append("3. 制定实施计划和培训方案\n");
        suggestions.append("4. 定期评估制度执行情况\n");

        return suggestions.toString();
    }

    /**
     * 构建外规内化提示词
     * 此方法保留用于兼容性，实际使用ragHelper.enhancePromptForRegulatoryPolicy
     */
    private String buildInternalizationPrompt(
        RegulationInternalizationRequestDTO request,
        String regulationContent,
        String industryPractices,
        String existingPolicies
    ) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请根据以下信息，将国家法规转化为企业内部管理制度：\n\n");
        prompt.append("【法规内容】\n").append(regulationContent).append("\n\n");
        prompt.append("【行业类型】\n").append(request.getIndustryType()).append("\n\n");
        prompt.append("【行业最佳实践】\n").append(industryPractices).append("\n\n");
        prompt.append("【企业现有制度】\n").append(existingPolicies).append("\n\n");
        prompt.append("【企业规模】\n").append(request.getCompanyScale()).append("\n\n");

        prompt.append("请生成符合以下要求的内部制度：\n");
        prompt.append("1. 完全符合国家法规要求\n");
        prompt.append("2. 结合行业特点和最佳实践\n");
        prompt.append("3. 适应企业规模和现状\n");
        prompt.append("4. 具有可操作性和实用性\n");
        prompt.append("5. 包含具体的执行流程和责任分工\n");

        return prompt.toString();
    }

    /**
     * 构建合规性验证提示词
     */
    private String buildComplianceValidationPrompt(String internalPolicy, String regulationContent) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请验证以下内部制度是否符合国家法规要求：\n\n");
        prompt.append("【内部制度】\n").append(internalPolicy).append("\n\n");
        prompt.append("【国家法规】\n").append(regulationContent).append("\n\n");

        prompt.append("请从以下方面进行验证：\n");
        prompt.append("1. 制度条款是否覆盖法规要求\n");
        prompt.append("2. 制度内容是否与法规冲突\n");
        prompt.append("3. 制度执行是否满足合规标准\n");
        prompt.append("4. 制度监督是否符合法规要求\n");
        prompt.append("5. 提出具体的改进建议\n");

        return prompt.toString();
    }
}
